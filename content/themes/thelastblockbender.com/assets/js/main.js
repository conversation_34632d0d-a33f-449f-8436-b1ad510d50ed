jQuery.noConflict();

(function ($) {
    var image = $('.jarallax-img');
    if (!image) return;

    var options = {
        disableParallax: /iPad|iPhone|iPod|Android/,
        disableVideo: /iPad|iPhone|iPod|Android/,
        speed: 0.1,
    };

    image.imagesLoaded(function () {
        image.parent().jarallax(options).addClass('initialized');
    });
})(jQuery);

(function ($) {
    'use strict';
    $('.featured-posts').owlCarousel({
        dots: false,
        margin: 30,
        nav: true,
        navText: [
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M20.547 22.107L14.44 16l6.107-6.12L18.667 8l-8 8 8 8 1.88-1.893z"></path></svg>',
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M11.453 22.107L17.56 16l-6.107-6.12L13.333 8l8 8-8 8-1.88-1.893z"></path></svg>',
        ],
        responsive: {
            0: {
                items: 1,
                slideBy: 1,
            },
            768: {
                items: 3,
                slideBy: 3,
            },
            992: {
                items: 4,
                slideBy: 4,
            },
        },
    });
})(jQuery);

// Owl Carousel Card functionality
(function ($) {
    'use strict';

    // Function to initialize custom carousels
    function initializeCustomCarousels() {
        $('.owl-carousel:not(.owl-loaded)').each(function() {
            var $carousel = $(this);

            // Get options from data attributes
            var options = {
                items: parseInt($carousel.data('items')) || 1,
                loop: $carousel.data('loop') !== false,
                margin: parseInt($carousel.data('margin')) || 10,
                nav: $carousel.data('nav') !== false,
                dots: $carousel.data('dots') !== false,
                autoplay: $carousel.data('autoplay') === true,
                autoplayTimeout: parseInt($carousel.data('autoplay-timeout')) || 5000,
                navText: [
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M20.547 22.107L14.44 16l6.107-6.12L18.667 8l-8 8 8 8 1.88-1.893z"></path></svg>',
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M11.453 22.107L17.56 16l-6.107-6.12L13.333 8l8 8-8 8-1.88-1.893z"></path></svg>',
                ],
                responsive: {
                    0: {
                        items: 1,
                        nav: options.nav
                    },
                    600: {
                        items: Math.min(parseInt($carousel.data('items')) || 1, 2),
                        nav: options.nav
                    },
                    1000: {
                        items: parseInt($carousel.data('items')) || 1,
                        nav: options.nav
                    }
                }
            };

            // Initialize the carousel
            $carousel.owlCarousel(options);
        });
    }

    // Initialize on document ready
    $(document).ready(function() {
        initializeCustomCarousels();
    });

    // Re-initialize when new content is added (for dynamic content)
    var observer = new MutationObserver(function(mutations) {
        var shouldReinit = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 &&
                        (node.classList.contains('owl-carousel') ||
                         node.querySelector && node.querySelector('.owl-carousel:not(.owl-loaded)'))) {
                        shouldReinit = true;
                    }
                });
            }
        });

        if (shouldReinit) {
            setTimeout(initializeCustomCarousels, 100);
        }
    });

    if (document.body) {
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

})(jQuery);

// Mouse parallax effect for background
document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    // Calculate offset (-10% to +10%)
    const offsetX = (mouseX - 0.5) * 100; // 20% range centered at 0
    const offsetY = (mouseY - 0.5) * 100;
    
    // Apply to body background
    document.body.style.backgroundPosition = `${50 + offsetX}% ${50 + offsetY}%`;
});
