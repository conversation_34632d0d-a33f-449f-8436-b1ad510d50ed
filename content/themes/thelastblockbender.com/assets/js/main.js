jQuery.noConflict();

(function ($) {
    var image = $('.jarallax-img');
    if (!image) return;

    var options = {
        disableParallax: /iPad|iPhone|iPod|Android/,
        disableVideo: /iPad|iPhone|iPod|Android/,
        speed: 0.1,
    };

    image.imagesLoaded(function () {
        image.parent().jarallax(options).addClass('initialized');
    });
})(jQuery);

(function ($) {
    'use strict';
    $('.featured-posts').owlCarousel({
        dots: false,
        margin: 30,
        nav: true,
        navText: [
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M20.547 22.107L14.44 16l6.107-6.12L18.667 8l-8 8 8 8 1.88-1.893z"></path></svg>',
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M11.453 22.107L17.56 16l-6.107-6.12L13.333 8l8 8-8 8-1.88-1.893z"></path></svg>',
        ],
        responsive: {
            0: {
                items: 1,
                slideBy: 1,
            },
            768: {
                items: 3,
                slideBy: 3,
            },
            992: {
                items: 4,
                slideBy: 4,
            },
        },
    });
})(jQuery);

// Mouse parallax effect for background
document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    // Calculate offset (-10% to +10%)
    const offsetX = (mouseX - 0.5) * 100; // 20% range centered at 0
    const offsetY = (mouseY - 0.5) * 100;
    
    // Apply to body background
    document.body.style.backgroundPosition = `${50 + offsetX}% ${50 + offsetY}%`;
});




//Embla sliders
// Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
      // Arrow Button Functions
      function addTogglePrevNextBtnsActive(emblaApi, prevBtn, nextBtn) {
        const togglePrevNextBtnsState = () => {
          if (emblaApi.canScrollPrev()) prevBtn.removeAttribute('disabled')
          else prevBtn.setAttribute('disabled', 'disabled')

          if (emblaApi.canScrollNext()) nextBtn.removeAttribute('disabled')
          else nextBtn.setAttribute('disabled', 'disabled')
        }

        emblaApi
          .on('select', togglePrevNextBtnsState)
          .on('init', togglePrevNextBtnsState)
          .on('reInit', togglePrevNextBtnsState)

        return () => {
          prevBtn.removeAttribute('disabled')
          nextBtn.removeAttribute('disabled')
        }
      }

      function addPrevNextBtnsClickHandlers(emblaApi, prevBtn, nextBtn, onButtonClick) {
        const scrollPrev = () => {
          emblaApi.scrollPrev()
          if (onButtonClick) onButtonClick(emblaApi)
        }
        const scrollNext = () => {
          emblaApi.scrollNext()
          if (onButtonClick) onButtonClick(emblaApi)
        }
        prevBtn.addEventListener('click', scrollPrev, false)
        nextBtn.addEventListener('click', scrollNext, false)

        const removeTogglePrevNextBtnsActive = addTogglePrevNextBtnsActive(
          emblaApi,
          prevBtn,
          nextBtn
        )

        return () => {
          removeTogglePrevNextBtnsActive()
          prevBtn.removeEventListener('click', scrollPrev, false)
          nextBtn.removeEventListener('click', scrollNext, false)
        }
      }

      // Dot Button Functions
      function addDotBtnsAndClickHandlers(emblaApi, dotsNode, onButtonClick) {
        let dotNodes = []

        const addDotBtnsWithClickHandlers = () => {
          dotsNode.innerHTML = emblaApi
            .scrollSnapList()
            .map(() => '<button class="embla__dot" type="button"></button>')
            .join('')

          const scrollTo = (index) => {
            emblaApi.scrollTo(index)
            if (onButtonClick) onButtonClick(emblaApi)
          }

          dotNodes = Array.from(dotsNode.querySelectorAll('.embla__dot'))
          dotNodes.forEach((dotNode, index) => {
            dotNode.addEventListener('click', () => scrollTo(index), false)
          })
        }

        const toggleDotBtnsActive = () => {
          const previous = emblaApi.previousScrollSnap()
          const selected = emblaApi.selectedScrollSnap()
          dotNodes[previous].classList.remove('embla__dot--selected')
          dotNodes[selected].classList.add('embla__dot--selected')
        }

        emblaApi
          .on('init', addDotBtnsWithClickHandlers)
          .on('reInit', addDotBtnsWithClickHandlers)
          .on('init', toggleDotBtnsActive)
          .on('reInit', toggleDotBtnsActive)
          .on('select', toggleDotBtnsActive)

        return () => {
          dotsNode.innerHTML = ''
        }
      }

      // Main Carousel Setup
      const OPTIONS = { loop: true }

      const emblaNode = document.querySelector('.embla')
      const viewportNode = emblaNode.querySelector('.embla__viewport')
      const prevBtnNode = emblaNode.querySelector('.embla__button--prev')
      const nextBtnNode = emblaNode.querySelector('.embla__button--next')
      const dotsNode = emblaNode.querySelector('.embla__dots')

      const emblaApi = EmblaCarousel(viewportNode, OPTIONS, [EmblaCarouselAutoplay()])

      const onNavButtonClick = (emblaApi) => {
        const autoplay = emblaApi?.plugins()?.autoplay
        if (!autoplay) return

        const resetOrStop =
          autoplay.options.stopOnInteraction === false
            ? autoplay.reset
            : autoplay.stop

        resetOrStop()
      }

      const removePrevNextBtnsClickHandlers = addPrevNextBtnsClickHandlers(
        emblaApi,
        prevBtnNode,
        nextBtnNode,
        onNavButtonClick
      )
      const removeDotBtnsAndClickHandlers = addDotBtnsAndClickHandlers(
        emblaApi,
        dotsNode,
        onNavButtonClick
      )

      emblaApi.on('destroy', removePrevNextBtnsClickHandlers)
      emblaApi.on('destroy', removeDotBtnsAndClickHandlers)
    })