<!-- 
Owl Carousel Template for Ghost HTML Card
Copy and paste this template into a Ghost HTML card to create an image carousel.

Instructions:
1. In Ghost editor, add an HTML card
2. Copy and paste this template
3. Replace the image URLs and content with your own
4. Customize the data attributes to control carousel behavior

Data Attributes:
- data-items: Number of items to show (default: 1)
- data-loop: Enable/disable infinite loop (default: true)
- data-margin: Space between items in pixels (default: 10)
- data-nav: Show/hide navigation arrows (default: true)
- data-dots: Show/hide dot indicators (default: true)
- data-autoplay: Enable/disable autoplay (default: false)
- data-autoplay-timeout: Autoplay interval in milliseconds (default: 5000)
-->

<!-- Basic Carousel Example -->
<div class="owl-carousel" 
     data-items="1" 
     data-loop="true" 
     data-margin="10" 
     data-nav="true" 
     data-dots="true" 
     data-autoplay="false">
     
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/800x400/FF6B6B/FFFFFF?text=Slide+1" alt="Slide 1">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">First Slide Title</h3>
            <p class="kg-owl-slide-description">This is the description for the first slide. You can include any HTML content here.</p>
        </div>
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/800x400/4ECDC4/FFFFFF?text=Slide+2" alt="Slide 2">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">Second Slide Title</h3>
            <p class="kg-owl-slide-description">This is the description for the second slide. You can customize the styling with CSS.</p>
        </div>
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/800x400/45B7D1/FFFFFF?text=Slide+3" alt="Slide 3">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">Third Slide Title</h3>
            <p class="kg-owl-slide-description">This is the description for the third slide. Add as many slides as you need.</p>
        </div>
    </div>
</div>

<!-- Multi-item Carousel Example -->
<div class="owl-carousel" 
     data-items="3" 
     data-loop="true" 
     data-margin="20" 
     data-nav="true" 
     data-dots="true" 
     data-autoplay="true" 
     data-autoplay-timeout="3000">
     
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Item+1" alt="Item 1">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">Item 1</h3>
            <p class="kg-owl-slide-description">Multi-item carousel example.</p>
        </div>
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Item+2" alt="Item 2">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">Item 2</h3>
            <p class="kg-owl-slide-description">Shows multiple items at once.</p>
        </div>
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/400x300/45B7D1/FFFFFF?text=Item+3" alt="Item 3">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">Item 3</h3>
            <p class="kg-owl-slide-description">Responsive design included.</p>
        </div>
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/400x300/96CEB4/FFFFFF?text=Item+4" alt="Item 4">
        <div class="kg-owl-slide-content">
            <h3 class="kg-owl-slide-title">Item 4</h3>
            <p class="kg-owl-slide-description">Add more items as needed.</p>
        </div>
    </div>
</div>

<!-- Simple Image-only Carousel -->
<div class="owl-carousel" 
     data-items="1" 
     data-loop="true" 
     data-margin="0" 
     data-nav="true" 
     data-dots="true" 
     data-autoplay="true" 
     data-autoplay-timeout="4000">
     
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/800x400/FF6B6B/FFFFFF?text=Image+1" alt="Image 1">
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/800x400/4ECDC4/FFFFFF?text=Image+2" alt="Image 2">
    </div>
    
    <div class="kg-owl-slide">
        <img src="https://via.placeholder.com/800x400/45B7D1/FFFFFF?text=Image+3" alt="Image 3">
    </div>
</div>

<!-- 
Usage Notes:
1. Replace placeholder images with your actual image URLs
2. Modify the slide content (titles and descriptions) as needed
3. Adjust data attributes to customize carousel behavior
4. You can remove the kg-owl-slide-content div if you only want images
5. The carousel will automatically initialize when the page loads

Customization Options:
- To hide navigation arrows: data-nav="false"
- To hide dots: data-dots="false"
- To disable looping: data-loop="false"
- To enable autoplay: data-autoplay="true"
- To show multiple items: data-items="2" or data-items="3" etc.
- To add space between items: data-margin="20"
-->
