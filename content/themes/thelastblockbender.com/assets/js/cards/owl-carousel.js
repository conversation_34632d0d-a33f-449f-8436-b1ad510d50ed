/**
 * Owl Carousel Card for Ghost
 * Automatically converts specially formatted HTML into Owl Carousels
 */

(function() {
    'use strict';

    // Ghost Card definition for server-side rendering
    const owlCarouselCard = {
        name: 'owl-carousel',
        type: 'dom',

        render: function({payload, env: {dom}, options = {}}) {
            if (!payload.slides || !Array.isArray(payload.slides) || payload.slides.length === 0) {
                return dom.createTextNode('');
            }

            // Create main container
            const container = dom.createElement('div');
            container.setAttribute('class', 'kg-card kg-owl-carousel-card');

            // Create carousel wrapper
            const carouselWrapper = dom.createElement('div');
            carouselWrapper.setAttribute('class', 'kg-owl-carousel-wrapper');

            // Create owl carousel container
            const owlCarousel = dom.createElement('div');
            owlCarousel.setAttribute('class', 'owl-carousel kg-owl-carousel');

            // Set data attributes for carousel options
            const carouselOptions = {
                items: payload.items || 1,
                loop: payload.loop !== false,
                margin: payload.margin || 10,
                nav: payload.nav !== false,
                dots: payload.dots !== false,
                autoplay: payload.autoplay || false,
                autoplayTimeout: payload.autoplayTimeout || 5000,
                responsive: payload.responsive || {
                    0: { items: 1 },
                    600: { items: payload.items || 1 },
                    1000: { items: payload.items || 1 }
                }
            };

            // Set data attributes for JavaScript initialization
            owlCarousel.setAttribute('data-owl-options', JSON.stringify(carouselOptions));

            // Create slides
            payload.slides.forEach(function(slide, index) {
                const slideElement = dom.createElement('div');
                slideElement.setAttribute('class', 'kg-owl-slide');

                if (slide.image && slide.image.src) {
                    const img = dom.createElement('img');
                    img.setAttribute('src', slide.image.src);
                    img.setAttribute('alt', slide.image.alt || '');
                    img.setAttribute('loading', 'lazy');

                    if (slide.image.width && slide.image.height) {
                        img.setAttribute('width', slide.image.width);
                        img.setAttribute('height', slide.image.height);
                    }

                    slideElement.appendChild(img);
                }

                // Add text content if provided
                if (slide.title || slide.description) {
                    const textContainer = dom.createElement('div');
                    textContainer.setAttribute('class', 'kg-owl-slide-content');

                    if (slide.title) {
                        const title = dom.createElement('h3');
                        title.setAttribute('class', 'kg-owl-slide-title');
                        title.appendChild(dom.createTextNode(slide.title));
                        textContainer.appendChild(title);
                    }

                    if (slide.description) {
                        const description = dom.createElement('p');
                        description.setAttribute('class', 'kg-owl-slide-description');
                        description.appendChild(dom.createRawHTMLSection(slide.description));
                        textContainer.appendChild(description);
                    }

                    slideElement.appendChild(textContainer);
                }

                owlCarousel.appendChild(slideElement);
            });

            carouselWrapper.appendChild(owlCarousel);
            container.appendChild(carouselWrapper);

            // Add caption if provided
            if (payload.caption) {
                const caption = dom.createElement('figcaption');
                caption.setAttribute('class', 'kg-owl-carousel-caption');
                caption.appendChild(dom.createRawHTMLSection(payload.caption));
                container.appendChild(caption);
            }

            return container;
        },

        // Handle URL transformations for different environments
        absoluteToRelative: function(payload, options) {
            if (payload.slides && Array.isArray(payload.slides)) {
                payload.slides.forEach(function(slide) {
                    if (slide.image && slide.image.src) {
                        slide.image.src = slide.image.src.replace(options.siteUrl, '');
                    }
                });
            }
            return payload;
        },

        relativeToAbsolute: function(payload, options) {
            if (payload.slides && Array.isArray(payload.slides)) {
                payload.slides.forEach(function(slide) {
                    if (slide.image && slide.image.src && !slide.image.src.startsWith('http')) {
                        slide.image.src = options.siteUrl + slide.image.src;
                    }
                });
            }
            return payload;
        }
    };

    // Client-side functionality for converting HTML to carousels

    // Function to convert HTML carousel markup to Owl Carousel
    function convertHtmlToCarousel(element) {
        // Look for carousel data attributes
        const items = element.getAttribute('data-items') || '1';
        const loop = element.getAttribute('data-loop') !== 'false';
        const margin = element.getAttribute('data-margin') || '10';
        const nav = element.getAttribute('data-nav') !== 'false';
        const dots = element.getAttribute('data-dots') !== 'false';
        const autoplay = element.getAttribute('data-autoplay') === 'true';
        const autoplayTimeout = element.getAttribute('data-autoplay-timeout') || '5000';

        const options = {
            items: parseInt(items),
            loop: loop,
            margin: parseInt(margin),
            nav: nav,
            dots: dots,
            autoplay: autoplay,
            autoplayTimeout: parseInt(autoplayTimeout),
            responsive: {
                0: { items: 1 },
                600: { items: Math.min(parseInt(items), 2) },
                1000: { items: parseInt(items) }
            }
        };

        // Initialize Owl Carousel
        if (typeof $ !== 'undefined' && $.fn.owlCarousel) {
            $(element).owlCarousel(options);
        }
    }

    // Function to initialize all carousels
    function initializeCarousels() {
        // Initialize carousels with data-owl-options attribute
        const carouselsWithOptions = document.querySelectorAll('.kg-owl-carousel[data-owl-options]');
        carouselsWithOptions.forEach(function(carousel) {
            if (typeof $ !== 'undefined' && $.fn.owlCarousel) {
                try {
                    const options = JSON.parse(carousel.getAttribute('data-owl-options'));
                    $(carousel).owlCarousel(options);
                } catch (e) {
                    console.error('Error initializing owl carousel:', e);
                }
            }
        });

        // Initialize carousels with HTML markup (for user-created carousels)
        const htmlCarousels = document.querySelectorAll('.owl-carousel:not(.owl-loaded)');
        htmlCarousels.forEach(function(carousel) {
            convertHtmlToCarousel(carousel);
        });
    }

    // Function to create a carousel from HTML card content
    function createCarouselFromHtml() {
        // Look for HTML cards that contain carousel markup
        const htmlCards = document.querySelectorAll('.kg-html-card');

        htmlCards.forEach(function(card) {
            const carouselElements = card.querySelectorAll('.owl-carousel:not(.owl-loaded)');
            carouselElements.forEach(function(carousel) {
                convertHtmlToCarousel(carousel);
            });
        });
    }

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            initializeCarousels();
            createCarouselFromHtml();
        });
    } else {
        initializeCarousels();
        createCarouselFromHtml();
    }

    // Re-initialize when new content is added (for dynamic content)
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            const carousels = node.querySelectorAll ?
                                node.querySelectorAll('.owl-carousel:not(.owl-loaded)') : [];

                            if (carousels.length > 0) {
                                setTimeout(function() {
                                    initializeCarousels();
                                    createCarouselFromHtml();
                                }, 100);
                            }
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Export for Ghost if in Node.js environment
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = owlCarouselCard;
    }

    // Make available globally for browser
    if (typeof window !== 'undefined') {
        window.owlCarouselCard = owlCarouselCard;
        window.initializeCarousels = initializeCarousels;
    }

})();
