/**
 * Owl Carousel Card Styles
 * Custom styling for the Ghost Editor Owl Carousel card
 */

/* Main card container */
.kg-card.kg-owl-carousel-card {
    position: relative;
    margin: 1.5em 0;
    width: 100%;
}

.kg-card.kg-owl-carousel-card * {
    box-sizing: border-box;
}

/* Carousel wrapper */
.kg-owl-carousel-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Owl carousel container */
.kg-owl-carousel {
    position: relative;
    width: 100%;
}

/* Individual slide styling */
.kg-owl-slide {
    position: relative;
    display: block;
    width: 100%;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
}

.kg-owl-slide img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.kg-owl-slide:hover img {
    transform: scale(1.02);
}

/* Slide content (text overlay) */
.kg-owl-slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    text-align: left;
}

.kg-owl-slide-title {
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.3;
    margin: 0 0 0.5rem 0;
    color: white;
}

.kg-owl-slide-description {
    font-size: 0.95rem;
    line-height: 1.4;
    margin: 0;
    opacity: 0.9;
    color: white;
}

.kg-owl-slide-description p {
    margin: 0;
}

/* Navigation arrows */
.kg-owl-carousel .owl-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    pointer-events: none;
}

.kg-owl-carousel .owl-nav button {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    font-size: 18px;
    color: #333;
    cursor: pointer;
    pointer-events: all;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.kg-owl-carousel .owl-nav button:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.kg-owl-carousel .owl-nav button.owl-prev {
    left: 15px;
}

.kg-owl-carousel .owl-nav button.owl-next {
    right: 15px;
}

.kg-owl-carousel .owl-nav button:before {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
}

.kg-owl-carousel .owl-nav button.owl-prev:before {
    border-width: 6px 10px 6px 0;
    border-color: transparent #333 transparent transparent;
    margin-left: -2px;
}

.kg-owl-carousel .owl-nav button.owl-next:before {
    border-width: 6px 0 6px 10px;
    border-color: transparent transparent transparent #333;
    margin-right: -2px;
}

/* Dots pagination */
.kg-owl-carousel .owl-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    gap: 8px;
}

.kg-owl-carousel .owl-dots button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: #ccc;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.kg-owl-carousel .owl-dots button.active,
.kg-owl-carousel .owl-dots button:hover {
    background: var(--ghost-accent-color, #15171a);
    transform: scale(1.2);
}

/* Caption */
.kg-owl-carousel-caption {
    text-align: center;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #666;
    margin-top: 1rem;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kg-owl-carousel .owl-nav button {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
    
    .kg-owl-carousel .owl-nav button.owl-prev {
        left: 10px;
    }
    
    .kg-owl-carousel .owl-nav button.owl-next {
        right: 10px;
    }
    
    .kg-owl-slide-content {
        padding: 1.5rem 1rem 1rem;
    }
    
    .kg-owl-slide-title {
        font-size: 1.2rem;
    }
    
    .kg-owl-slide-description {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .kg-owl-carousel .owl-nav button {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
    
    .kg-owl-slide-content {
        padding: 1rem 0.75rem 0.75rem;
    }
    
    .kg-owl-slide-title {
        font-size: 1.1rem;
    }
}

/* Loading state */
.kg-owl-carousel.owl-loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.kg-owl-carousel.owl-loaded {
    opacity: 1;
}

/* Accessibility improvements */
.kg-owl-carousel .owl-nav button:focus,
.kg-owl-carousel .owl-dots button:focus {
    outline: 2px solid var(--ghost-accent-color, #15171a);
    outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .kg-owl-slide {
        background: #1a1a1a;
    }
    
    .kg-owl-carousel .owl-nav button {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    
    .kg-owl-carousel .owl-nav button:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .kg-owl-carousel .owl-nav button.owl-prev:before {
        border-color: transparent #fff transparent transparent;
    }
    
    .kg-owl-carousel .owl-nav button.owl-next:before {
        border-color: transparent transparent transparent #fff;
    }
}
