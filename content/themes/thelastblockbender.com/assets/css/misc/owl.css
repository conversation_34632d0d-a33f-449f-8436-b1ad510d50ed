.owl {
    position: relative;
    z-index: 1;
    display: none;
    width: 100%;
    -webkit-tap-highlight-color: transparent;
}

.owl .owl-stage {
    position: relative;
    touch-action: pan-y;
}

.owl .owl-stage::after {
    display: block;
    height: 0;
    clear: both;
    line-height: 0;
    visibility: hidden;
    content: ".";
}

.owl .owl-stage-outer {
    position: relative;
    overflow: hidden;
    transform: translate3d(0, 0, 0);
}

.owl .owl-item {
    position: relative;
    float: left;
    min-height: 1px;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

.owl .owl-item > img {
    display: block;
    transform-style: preserve-3d;
    width: 100%;
}

.owl .owl-nav.disabled,
.owl .owl-dots.disabled {
    display: none;
}

.owl .owl-prev,
.owl .owl-next,
.owl .owl-dot {
    cursor: pointer;
    user-select: none;
}

.owl .owl-prev,
.owl .owl-next {
    position: absolute;
    top: -86px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    padding: 0;
    font-size: 18px;
    color: var(--dark-gray-color);
    text-align: center;
    background-color: var(--white-color);
    border: 1px solid var(--mid-gray-color);
    border-radius: 3px;
    outline: none;
    transition: color 0.3s var(--animation-base);
}

.owl .owl-prev.disabled,
.owl .owl-next.disabled {
    color: var(--secondary-text-color);
    cursor: default;
}

.owl .owl-prev {
    right: 34px;
}

.owl .owl-next {
    right: 0;
}

.owl .owl-dots {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.owl .owl-dot {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 0;
    border: 0;
    outline: none;
}

.owl .owl-dot span {
    width: 8px;
    height: 8px;
    background-color: var(--mid-gray-color);
    border-radius: 50%;
}

.owl .owl-dot.active span {
    background-color: var(--black-color);
}

.owl.owl-loaded {
    display: block;
}

.owl.owl-loading {
    display: block;
    opacity: 0;
}

.owl.owl-hidden {
    opacity: 0;
}

.owl.owl-refresh .owl-item {
    visibility: hidden;
}

.owl.owl-drag .owl-item {
    user-select: none;
}

.owl.owl-grab {
    cursor: move;
}

.no-js .owl {
    display: block;
}

/* Owl Carousel Card Styles */
.kg-card.kg-owl-carousel-card {
    position: relative;
    margin: 1.5em 0;
    width: 100%;
}

.kg-card.kg-owl-carousel-card * {
    box-sizing: border-box;
}

/* Carousel wrapper */
.kg-owl-carousel-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Owl carousel container */
.kg-owl-carousel {
    position: relative;
    width: 100%;
}

/* Individual slide styling */
.kg-owl-slide {
    position: relative;
    display: block;
    width: 100%;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
}

.kg-owl-slide img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.kg-owl-slide:hover img {
    transform: scale(1.02);
}

/* Slide content (text overlay) */
.kg-owl-slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    text-align: left;
}

.kg-owl-slide-title {
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.3;
    margin: 0 0 0.5rem 0;
    color: white;
}

.kg-owl-slide-description {
    font-size: 0.95rem;
    line-height: 1.4;
    margin: 0;
    opacity: 0.9;
    color: white;
}

.kg-owl-slide-description p {
    margin: 0;
}

/* Custom carousel navigation (overrides for HTML carousels) */
.owl-carousel .owl-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    pointer-events: none;
    margin-top: 0;
}

.owl-carousel .owl-nav button {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    font-size: 18px;
    color: #333;
    cursor: pointer;
    pointer-events: all;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.owl-carousel .owl-nav button:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.owl-carousel .owl-nav button.owl-prev {
    left: 15px;
}

.owl-carousel .owl-nav button.owl-next {
    right: 15px;
}

/* Custom dots styling for HTML carousels */
.owl-carousel .owl-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    gap: 8px;
}

.owl-carousel .owl-dots button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: #ccc;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.owl-carousel .owl-dots button.active,
.owl-carousel .owl-dots button:hover {
    background: var(--ghost-accent-color, #15171a);
    transform: scale(1.2);
}

/* Caption */
.kg-owl-carousel-caption {
    text-align: center;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #666;
    margin-top: 1rem;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .owl-carousel .owl-nav button {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }

    .owl-carousel .owl-nav button.owl-prev {
        left: 10px;
    }

    .owl-carousel .owl-nav button.owl-next {
        right: 10px;
    }

    .kg-owl-slide-content {
        padding: 1.5rem 1rem 1rem;
    }

    .kg-owl-slide-title {
        font-size: 1.2rem;
    }

    .kg-owl-slide-description {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .owl-carousel .owl-nav button {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .kg-owl-slide-content {
        padding: 1rem 0.75rem 0.75rem;
    }

    .kg-owl-slide-title {
        font-size: 1.1rem;
    }
}

/* Loading state */
.owl-carousel.owl-loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.owl-carousel.owl-loaded {
    opacity: 1;
}

/* Accessibility improvements */
.owl-carousel .owl-nav button:focus,
.owl-carousel .owl-dots button:focus {
    outline: 2px solid var(--ghost-accent-color, #15171a);
    outline-offset: 2px;
}
